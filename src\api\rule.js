import request from "@/utils/request";
import Qs from "qs";

// 获取规则列表
export function getRuleList(query) {
  return request({
    url: "/get_rules",
    method: "get",
    params: query,
  });
}

// 获取对应id规则
export function getRule(query) {
  return request({
    url: "/get_rule",
    method: "get",
    params: query,
  });
}

// 修改对应id规则
export function updateRule(data) {
  data = Qs.stringify(data);
  return request({
    url: "/update_rule",
    method: "post",
    data,
  });
}
// 修改对应id规则是否忽略或启用状态
export function updateRuleEnabled(data) {
  data = Qs.stringify(data);
  return request({
    url: "rule_enabled/update",
    method: "post",
    data,
  });
}

// 测试美术资源规则
export function testRule(data) {
  data = Qs.stringify(data);
  return request({
    url: "/test_rule",
    method: "post",
    data,
  });
}

// 创建美术资源规则
export function createRule(data) {
  data = Qs.stringify(data);
  return request({
    url: "/create_rule",
    method: "post",
    data,
  });
}
// 获取美术资源规则类型
export function getRuleConfig(query) {
  return request({
    url: "/get_rule_config",
    method: "get",
    params: query,
  });
}

// 创建表格检查规则
export function createExcelRule(data) {
  data = Qs.stringify(data);
  return request({
    url: "/create_table_rule",
    method: "post",
    data,
  });
}
// 测试表格检测规则
export function testExcelRule(data) {
  data = Qs.stringify(data);
  return request({
    url: "/test_table_rule",
    method: "post",
    data,
  });
}

// 获取表格名列表
export function getExcelList(query) {
  return request({
    url: "/get_excel_list",
    method: "get",
    params: query,
  });
}
// 获取表格sheet列表
export function getExcelSheetList(query) {
  return request({
    url: "/get_sheet_info",
    method: "get",
    params: query,
  });
}
// 获取表格参数运算符
export function getExcelOperator() {
  return request({
    url: "/get_operator",
    method: "get",
  });
}
// 获取表格规则列表
export function getExcelRuleList(query) {
  return request({
    url: "/get_table_rules",
    method: "get",
    params: query,
  });
}
// 获取规则报告详细
export function getExcelRuleDetail(query) {
  return request({
    url: "/get_rule_detail",
    method: "get",
    params: query,
  });
}
// 更新修改对应id的表格规则
export function updateExcelRule(data) {
  data = Qs.stringify(data);
  return request({
    url: "/update_table_rule",
    method: "post",
    data,
  });
}

// 删除规则
export function deleteRule(data) {
  data = Qs.stringify(data);
  return request({
    url: "/delete_rule",
    method: "post",
    data,
  });
}
// 是否启用规则
export function updateRuleStatus(data) {
  data = Qs.stringify(data);
  return request({
    url: "/is_enabled",
    method: "post",
    data,
  });
}

// 获取表格检测规则类型
export function getExcelRuleConfig(query) {
  return request({
    url: "/get_config",
    method: "get",
    params: query,
  });
}
