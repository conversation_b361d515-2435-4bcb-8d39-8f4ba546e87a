export default {
  route: {
    dashboard: "首页",
    home: "首页",
    "报告管理": "报告管理",
    "规则管理": "规则管理",
    "资源检查": "资源检查",
    "分支管理": "分支管理",
    "项目管理":"项目详情",
    "报告概览":"报告概览",
    resourceDetect: "美术资源",
    tableDetect: "表格检查",
    myProject: "我的项目",
    projectDetail: "美术资源项目详情",
    tableDetectDetail: "表格检查项目详情",
    projectReport: "项目报告",
    tableProjectReport: "表格项目报告",
    ruleReport: "规则详细报告",
    reportDetail: "报告详细",
    reportComparison: "报告对比",
    rulesList: "规则列表",
    ruleEdit: "美术资源规则编辑",
    tableRuleEdit: "表格检查规则编辑",
    ruleCreate: "创建规则",
    permission: "权限配置",
    roleManagement: "角色管理",
    projectManagement: "美术项目管理",
    // tableManagement: "表格项目管理",
    tableManagement: "项目管理",
    userManagement: "用户管理",
    helpGuide: "帮助指引",
    helpGuide01: "帮助指引01",
    helpGuide02: "帮助指引02",
    profile: "个人中心",
  },
  navbar: {
    home: "首页",
    logOut: "退出登录",
    profile: "个人中心",
    theme: "换肤",
    size: "布局大小",
    switchLanguageTip: "已成功切换中文",
  },
  login: {
    title: "AssetsLint 平台登录",
    logIn: "登录",
    username: "账号",
    password: "密码",
    capsLocked: "已锁定大写",
    nameTip: "请正确输入用户名,且不能为空",
    passwordTip: "密码不能小于六位",
    any: "随便填",
    thirdparty: "第三方登录",
  },
  home: {
    automatedResourceTesting: "资源自动化检测平台",
    clickToHelpGuide: "前往帮助指引",
    demoVideo: "操作演示视频",
    platformModules: "平台功能模块",
    myProjects: "我的项目",
    checkAllProject: "查看所有管理的项目",
    rulesList: "规则列表",
    checkAllrules: "查看和删改所创建的规则",
    createRule: "创建规则",
    createNewRule: "创建新的测试规则参数",
    automatedResource: "资源自动化检测",
    introduction: "介绍",
    assetsLint: "资源检测 （AssetsLint）",
    assetsLintText:
      "针对市面上主流手游引擎(unity,UE)提供完善的资源检测方案。主要分为资源扫描、规则编辑、前端展示三个大模块。",
    advantages: "优势",
    noCode: "无代码",
    noCodeText:
      "使用人员不需要了解底层代码实现，通过规则编辑编写规则即可，方便美术和QA同学。",
    ruleCompleteness: "规则完备性",
    ruleCompletenessText:
      "支持规则的与或非逻辑嵌套操作，实现了代码与规则的转换。",
    visualisation: "可视化",
    visualisationText: "扫描数据可视化，方便查看问题点，以及长线数据跟踪。",
    continuousIntegration: "持续集成",
    continuousIntegrationText:
      "规则持久化到数据库，通过实现对规则的解码，实现持续集成，增量扫描等。",
    ruleEditor: "AssetsLint 规则编辑器",
    serialisation: "序列化",
    deserialisation: "反序列化",
    goCreateRule: "前往创建规则",
  },
  project: {
    myProject: "我的项目",
    latestVersion: "最新版本",
    errorCount: "告警数",
    lastScanned: "最近扫描时间",
    versions: "版本",
    allVersions: "所有版本",
    uploadReport: "上传分支报告文件",
    uploadTips1: "将文件拖到此处，或",
    uploadTips2: "点击上传",
    uploading: "正在上传中",
    upload: "上传",
    close: "关闭",
    fileExists: "该文件已存在",
    existed: "已存在",
    selectFiles: "请选取配置文件后再添加",
    uploadSuccess: "上传成功",
    noData: "暂无",

    addBranch: "添加/删除分支",
    branchName: "分支名称",
    enterBranchName: "请输入分支名称",
    uploadRuleConfig: "上传配置文件",
    clickSelect: "点击选取",
    addBranchSuccess: "添加分支成功!",
    branchExists: "分支名已存在!",
    wrongFormat: "rule_config.json格式错误,请用utf-8保存!",
    cancel: "取 消",
    delete: "删 除",
    add: "添 加",
    uploadTips: "若删除分支，则无需上传",
    isDeleteBranch: "确定删除此分支?",
  },
  projectDetail: {
    backMyProject: "返回我的项目",
    report: "报告",
    projectDetail: "报告详情",
    projectName: "项目名称",
    lastScanned: "最近扫描时间",
    totalResources: "资源总数",
    totalResourcesSize: "资源总大小",
    errorCount: "告警数",
    resourceComposition: "资源构成",
    resourceData: "资源数据",
    resourceSize: "资源大小",
    resourceErrorCount: "资源告警数",
    resourceType: "资源类型",
    total: "总数",
    reportList: "报告列表",
    isGoRulesList: "前往规则列表页？",
    viewRulesList: "查看规则列表",
    compareReports: "对比选中报告",
    selectTwoReport: "请选中两项报告才能进行对比",
    selectAnotherReport: "请再选中一项报告才能进行对比",
    testId: "测试ID",
    testVersion: "测试版本",
    dateTime: "时间",
    searchTestId: "输入测试ID搜索",
    accessReport: "进入报告",
    startScan: "开始扫描",
    reportScan: "报告扫描",
    scanProgress: "扫描进度",
    scanTime: "扫描用时",
    ok: "确 定",
    min: "分",
    second: "秒",
    hour: "时",
    isStartScan: "是否开始扫描?",
    editResourceGit: "编辑资源仓库",
  },
  reportDetail: {
    backToProjectReport: "返回项目报告",
    reportDetail: "报告详细",
    testId: "测试ID",
    scanTime: "扫描时间",
    testVersion: "测试版本",
    errorCount: "告警数",
    reportList: "报告列表",
    filterType: "筛选类型",
    chooseType: "请选择类型",
    searchKeyWords: "请输入关键词搜索",
    errorList: "报错列表",
    operation: "操作",
    viewMore: "查看更多",
    detailedData: "数据详细",
    switchFieldName: "切换字段语言",
  },
  reportComparison: {
    backToProjectReport: "返回项目报告",
    reportComparison: "报告对比",
    testId: "测试ID",
    comparedTestId: " 对比 测试ID",
    extraData: " 报告多出的数据",
    missingData: " 报告缺少的数据",
  },
  rulesList: {
    rulesList: "规则列表",
    createRule: "新建规则",
    deleteRule: "删除选中规则",
    filterProject: "筛选项目",
    selectProject: "请选择项目",
    selectBranch: "请选择项目分支",
    ruleCategories: "规则分类",
    tableName: "\u3000表格名",
    selectRuleCategory: "请选择",
    allType: "全部",
    selectAll: "全选",
    goToCreateRule: "前往创建规则页?",
    operateSuccess:"操作成功"
  },
  ruleEdit: {
    backToRulesList: "返回规则列表",
    ruleEdit: "规则编辑",
  },
  ruleCreate: {
    ruleCreate: "创建规则",
    projectBranch: "项目分支",
    selectProject: "请选择项目",
    selectBranch: "请选择项目分支",
    missingBranch: "缺少项目分支,请上传!",
    missingRuleConfig: "缺少规则配置参数,请上传!",
    ruleCategories: "规则类型",
    tableName: "表格名",
    switchTypeTips: "切换将清空所有参数",
    chooseRuleType: "请选择",
    ruleName: "规则名称",
    enterName: "请输入名称",
    enabled: "已启用规则",
    enable: "启用此规则",
    ignore: "忽略此规则",
    ignored: "已忽略规则",
    conditionParameter: "条件参数",
    targetParameter: "目标参数",
    testRule: "测试规则",
    importRule: "导入规则",
    downloadRule: "下载规则",
    saveRule: "保存规则",
    projectBranchRequired: "项目及分支必选",
    ruleCategoriesRequired: "规则类型必选",
    tableNameRequired: "表格名必选",
    ruleNameRequired: "规则名不能为空",
    tips: "提示",
    switched: "已切换!",
    cancelSwitch: "已取消切换!",
    conditionGroupNoConditionTips:
      "条件参数有条件组没有添加条件，请将条件设置完整！",
    conditionNoOperatorTips:
      "条件参数有操作符未选择，请检查所有填写项是否均已填写！",
    conditionNoValueTips:
      "条件参数的值设定不全，请检查所有填写项是否均已填写！",
    assertsGroupNoConditionTips:
      "目标参数有条件组没有添加条件，请将条件设置完整！",
    assertsNoOperatorTips:
      "目标参数有操作符未选择，请检查所有填写项是否均已填写！",
    assertsNoValueTips: "目标参数的值设定不全，请检查所有填写项是否均已填写！",
    requiredNotEmptyTips: "必填项不能为空，请填写完整！",
    targetParameterAddConditionTips: "请在目标参数至少添加一个条件组！",
    isExportJsonFile: "是否导出JSON格式文件？",
    downloadStarted: "已开始下载",
    exportCanceled: "已取消导出",
    isSaveRule: "是否保存规则？",
    saved: "保存成功!",
    saveCanceled: "已取消保存",

    addConditionGroup: "添加条件组",
    addCondition: "添加条件",
    deleteConditionGroup: "删除条件组",
    forward: "正向",
    reverse: "反向",
    SatisfiesRegularity: "满足正则",
    include: "包含",
    withinRange: "在范围内",
    inSet: "在集合中",
    choose: "请选择",
    enterNumber: "请输入数字",
    enterCommaSeparatedValues: "可输入多个值，用英文逗号隔开", // 新增：支持 in 操作符的逗号分隔输入
    enterCharacters: "请输入字符",
    insertConditionGroup: "插入条件组",
    isDelCondition: "将删除该条件, 是否继续?",
    isDelConditionGroup: "将删除该条件组, 是否继续?",
    deleted: "删除成功",
    cancelDelete: "已取消删除",

    import: "导入",
    selectProjectBranch: "请选择项目及分支",
    uploadTips1: "将文件拖到此处，或",
    uploadTips2: "点击上传",
    uploadJson: "上传json文件，且只能上传 1 个文件",
    ok: "确定",
    cancel: "取消",
    onlyJsonTips: "每次只能导入一个json文件!",
    noDataTips: "没有数据，请重新选择文件上传！",
    tips: "提示",
    fileRemoved: "已移除文件!",
    coverageDataTips: "导入后原数据会被覆盖，确认导入吗?",
    importSucceeded: "导入成功",
    importError: "Error!无法找到对应规则类型,请确认项目及分支!",
  },
  testRule: {
    testRule: "测试规则",
    errorList: "报错列表",
    operate: "操作",
    checkMore: "查看更多",
    dataDetails: "数据详细",
  },
  permissionRole: {
    roleManagement: "角色管理",
    addRole: "添加角色",
    roleId: "角色ID",
    roleKey: "角色标识",
    roleName: "角色名称",
    roleDescription: "角色描述",
    operator: "操作",
    edit: "编辑",
    delete: "删除",
    editRole: "编辑角色",
    pageList: "页面列表",
    enterRoleName: "请输入角色名",
    enterRoleKey: "请输入角色标识",
    enterText: "请输入描述文字",
    ok: "确定",
    cancel: "取消",
    characterLength: "长度在 3 到 15 个字符",
    cannotSpecialCharacters: "不能含有中文或特殊字符",
    isDeleteRole: "确认删除此权限角色吗?",
    tips: "提示",
    editSuccessful: "编辑成功",
    roleNameExists: "角色名已存在,请重新输入!",
    roleKeyExists: "角色标识已存在,请重新输入!",
    addSuccessful: "添加成功",
    deleteSuccessful: "删除成功!",
    RequiredTips: "填写信息不完整/不正确，请确认！",
  },
  permissionProject: {
    projectManagement: "美术资源项目管理",
    tableManagement: "表格检查项目管理",
    addProject: "添加项目",
    projectLogo: "项目Logo",
    projectId: "项目ID",
    projectName: "项目名称",
    projectDescription: "项目描述",
    ignorePath: "资源忽略路径",
    createTime: "创建时间",
    editTime: "最近修改时间",
    operator: "操作",
    setUser: "配属用户",
    edit: "编辑",
    delete: "删除",
    editProject: "编辑项目",
    enterProjectName: "请输入项目名称",
    add: "添加",
    enterProjectDescription: "请输入项目描述文字",
    ok: "确定",
    cancel: "取消",

    belongingUser: "所属用户",
    userName: "用户名",
    enterUserName: "输入用户名",
    search: "查询",
    existingUser: "已有用户",
    createNewUser: "创建新用户",
    isGoManagement: "是否前往用户管理页?",
    batchOperation: "批量操作",
    nickName: "昵称",
    state: "用户状态",
    normal: "正常",
    disabled: "未启用",
    disassociate: "取消关联",
    isDisassociate: "确定取消关联?",
    close: "关 闭",
    comfirmAdd: "确认添加",
    addSuccess: "添加成功",
    cancelled: "取消成功",

    characterLength: "长度在 3 到 15 个字符",
    isDeleteProject: "确认删除此权限角色吗?",
    tips: "提示",
    editSuccessful: "编辑成功",
    addSuccessful: "添加成功",
    deleteSuccessful: "删除成功!",
    RequiredTips: "填写信息不完整/不正确，请确认！",
    isJPG: "上传头像图片只能是 JPG/PNG/Webp/Gif 格式!",
    isLt2M: "上传头像图片大小不能超过 2MB!",
    existPath: "已存在相同路径!",
  },
  permissionUser: {
    userManagement: "用户管理",
    addUser: "添加用户",
    avatar: "头像",
    userId: "ID",
    userName: "用户名",
    nickName: "昵称",
    userRole: "用户角色",
    phone: "手机号",
    email: "邮箱",
    isEnabled: "是否启用",
    operator: "操作",
    edit: "编辑",
    delete: "删除",
    resetPwd: "重置密码",
    editUser: "编辑用户",
    enterUserName: "请输入用户名",
    enterNickName: "请输入昵称",
    selectRole: "请选择角色",
    enterPhone: "请输入手机号",
    enterEmail: "请输入邮箱地址",
    ok: "确定",
    cancel: "取消",

    characterLength: "长度在 3 到 15 个字符",
    phoneRegTips: "手机号格式不正确",
    emailRegTips: "邮箱格式不正确",
    isDeleteUser: "确认删除此用户吗?",
    tips: "提示",
    editSuccessful: "编辑成功",
    userNameExists: "用户名已存在,请重新输入!",
    addSuccessful: "添加成功",
    deleteSuccessful: "删除成功!",
    enabled: "启用成功",
    disabled: "禁用成功",
    RequiredTips: "填写信息不完整/不正确，请确认！",
    isJPG: "上传头像图片只能是 JPG/PNG/Webp/Gif 格式!",
    isLt2M: "上传头像图片大小不能超过 2MB!",
    isResetPwd: "是否将此用户密码重置为111111?",
    resetSuccessful: "密码已重置",
    cancelReset: "取消重置",
  },
  profile: {
    basicInformation: "基本信息",
    accountBind: "账号绑定",
    personalInformation: "个人信息",
    userName: "用户名 / 账号",
    nickName: "昵称",
    submit: "保存",
    isSubmit: "是否保存?",
    tips: "提示",
    submitSuccess: "保存成功!",
    userNameExists: "用户名已存在,请重新输入!",
    cancelled: "已取消",
    bindPhone: "绑定手机",
    boundPhone: "已绑定手机",
    modify: "立即修改",
    bindEmail: "绑定邮箱",
    boundEmail: "已绑定邮箱",
    modifyPassword: "修改密码",
    modifyLoginPassword: "修改登录密码",
    password: "原密码",
    enterPassword: "请输入原密码",
    passwordMinCharacters: "最少6个字符",
    newPassword: "新密码",
    enterNewPassword: "请输入新密码",
    confirmPassword: "确认密码",
    enterConfirmPassword: "请输入确认密码",
    ok: "确定",
    cancel: "取消",
    modifyPhone: "修改手机号",
    phone: "手机号",
    enterPhoneNum: "请输入手机号",
    change: "更改",
    email: "邮箱",
    enterEmail: "请输入邮箱地址",
    twoPasswordsInconsistent: "两次密码不一致",
    changeSuccessful: "更改成功!",
    isJPG: "上传头像图片只能是 JPG/PNG/Webp/Gif 格式!",
    isLt2M: "上传头像图片大小不能超过 2MB!",
    uploadSuccess: "上传成功",
  },
  uploadImage: {
    change: "更换",
    check: "查看",
  },
  backToTop: "返回顶部",
};
