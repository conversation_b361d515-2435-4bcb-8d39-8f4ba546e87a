<template>
  <div class="dropdown-selector-container">
    <!-- 分支选择下拉框 -->
    <div class="selector-item">
      <label class="selector-label">分支选择:</label>
      <el-select
        v-model="selectedBranch"
        placeholder="请选择分支"
        class="selector-dropdown"
        :loading="branchLoading"
        :disabled="branchLoading || branchOptions.length === 0"
        @change="handleBranchChange"
        @focus="handleBranchFocus"
      >
        <el-option
          v-for="branch in branchOptions"
          :key="branch.value"
          :label="branch.label"
          :value="branch.value"
        />
      </el-select>
    </div>

    <!-- 检查类型下拉框 -->
    <div class="selector-item">
      <label class="selector-label">检查类型:</label>
      <el-select
        v-model="selectedCheckType"
        placeholder="请选择检查类型"
        class="selector-dropdown"
        @change="handleCheckTypeChange"
      >
        <el-option
          v-for="checkType in checkTypeOptions"
          :key="checkType.value"
          :label="checkType.label"
          :value="checkType.value"
        />
      </el-select>
    </div>

    <!-- 分支为空提示弹窗 -->
    <el-dialog
      :visible.sync="showNoBranchDialog"
      title="提示"
      width="400px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :show-close="false"
      :append-to-body="true"
      center
      custom-class="no-branch-dialog"
    >
      <div class="no-branch-content">
        <i class="el-icon-warning-outline warning-icon"></i>
        <p class="warning-text">当前项目还未创建分支</p>
        <p class="suggestion-text">可前往分支管理页面创建分支</p>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="handleCancelDialog">取消</el-button>
        <el-button type="primary" @click="handleGoToBranchManagement">立即前往</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { mapGetters, mapActions } from 'vuex'
import { getProjectInfo } from '@/api/project'
export default {
  name: 'DropdownSelector',
  data() {
    return {
      showNoBranchDialog: false, // 控制分支为空提示弹窗显示
      hasShownNoBranchDialog: false, // 标记是否已经显示过提示弹窗
      dataInitialized: false // 标记数据是否已经初始化完成
    }
  },
  computed: {
    ...mapGetters('project', [
      'branchOptions',
      'checkTypeOptions',
      'loading'
    ]),
    // 使用computed属性来实现双向绑定
    selectedBranch: {
      get() {
        return this.$store.getters['project/selectedBranch']
      },
      set(value) {
        const projectId = this.$route.params.projectId || sessionStorage.getItem('currentProjectId')
        this.updateSelectedBranch({ branch: value, projectId })
      }
    },
    selectedCheckType: {
      get() {
        return this.$store.getters['project/selectedCheckType']
      },
      set(value) {
        this.updateSelectedCheckType(value)
      }
    },
    // 重命名loading为branchLoading以保持兼容性
    branchLoading() {
      return this.loading
    }
  },
  mounted() {
    // 初始化时更新项目信息
    const projectId = this.$route.params.projectId || sessionStorage.getItem('currentProjectId')
    if (projectId) {
      this.updateProjectInfo(projectId)
    }
    this.initBranchData()
  },
  watch: {
    // 监听路由中的 projectId 参数变化
    '$route.params.projectId': {
      handler(newProjectId, oldProjectId) {
        console.log('projectId 变化检测:', {
          newProjectId,
          oldProjectId,
          currentPath: this.$route.path
        })

        // 当 projectId 发生变化时重新加载数据
        if (newProjectId && newProjectId !== oldProjectId) {
          console.log('项目ID切换，从', oldProjectId, '到', newProjectId)

          // 立即清空当前状态
          this.clearCurrentState()

          // 重置弹窗状态和数据初始化状态
          this.hasShownNoBranchDialog = false
          this.dataInitialized = false

          // 更新项目信息（包括项目名称）
          this.updateProjectInfo(newProjectId)

          // 重新初始化分支数据
          this.initBranchData()
        }
      },
      immediate: false // 不在组件创建时立即执行
    },
    // 监听加载状态变化，只有在加载完成后才检查是否显示弹窗
    branchLoading: {
      handler(isLoading, wasLoading) {
        // 只有当从加载中变为加载完成时才检查弹窗
        if (wasLoading && !isLoading) {
          // 使用 nextTick 确保数据已经完全更新
          this.$nextTick(() => {
            this.checkAndShowNoBranchDialog(this.branchOptions)
          })
        }
      }
    }
  },
  methods: {
    ...mapActions('project', [
      'fetchBranchOptions',
      'updateSelectedBranch',
      'updateSelectedCheckType',
      'clearProjectData',
      'setProjectInfo'
    ]),

    // 初始化分支数据
    async initBranchData() {
      const projectId = this.$route.params.projectId || sessionStorage.getItem('currentProjectId')
      if (!projectId) {
        console.log('未找到projectId，跳过分支数据获取')
        this.dataInitialized = true
        return
      }

      try {
        console.log('开始获取分支数据，projectId:', projectId)
        await this.fetchBranchOptions(projectId)
        console.log('分支数据获取完成')

        // 标记数据初始化完成
        this.dataInitialized = true

        // 确保在数据获取完成后再检查弹窗
        // 使用 nextTick 和短暂延迟确保 store 中的数据已经完全更新到组件
        this.$nextTick(() => {
          // 添加短暂延迟，确保所有状态都已更新
          setTimeout(() => {
            this.checkAndShowNoBranchDialog(this.branchOptions)
          }, 100)
        })
      } catch (error) {
        console.error('获取分支数据失败:', error)
        this.$message.error('获取分支数据失败')

        // 即使获取失败，也标记为初始化完成
        this.dataInitialized = true

        // 即使获取失败，也要检查是否需要显示弹窗（此时分支选项应该为空）
        this.$nextTick(() => {
          // 添加短暂延迟，确保所有状态都已更新
          setTimeout(() => {
            this.checkAndShowNoBranchDialog(this.branchOptions)
          }, 100)
        })
      }
    },

    // 清空当前项目状态
    clearCurrentState() {
      // console.log('清空当前项目状态')

      // 立即清空 Vuex store 中的项目相关数据
      this.clearProjectData()

      // 清空组件本地状态
      this.showNoBranchDialog = false
      this.hasShownNoBranchDialog = false
      this.dataInitialized = false

      // console.log('状态清理完成')
    },

    // 分支选择变化处理
    handleBranchChange(value) {
      // console.log('选择的分支:', value)
      // 发送事件给父组件
      this.$emit('branch-change', value)
    },

    // 检查类型选择变化处理
    handleCheckTypeChange(value) {
      console.log('选择的检查类型:', value)
      this.$emit('check-type-change', value)
    },

    // 分支选择框获得焦点时处理
    handleBranchFocus() {
      // 移除在焦点事件中显示弹窗的逻辑，避免在数据加载过程中误触发
      // 弹窗显示逻辑统一在数据加载完成后处理
      // console.log('分支选择框获得焦点')
    },

    // 检查并显示分支为空提示弹窗
    checkAndShowNoBranchDialog(branchOptions) {
      // 只有在以下条件都满足时才显示弹窗：
      // 1. 分支选项为空（确保是数组且长度为0）
      // 2. 不在加载中（确保接口已经响应完毕）
      // 3. 还没有显示过弹窗（避免重复显示）
      // 4. 有项目ID（确保是在项目页面中）
      // 5. 组件已经挂载（避免在初始化过程中误触发）
      // 6. 数据已经初始化完成（确保不是在初始化过程中）
      const projectId = this.$route.params.projectId || sessionStorage.getItem('currentProjectId')

      console.log('检查弹窗条件:', {
        branchOptions,
        branchOptionsLength: Array.isArray(branchOptions) ? branchOptions.length : 'not array',
        branchLoading: this.branchLoading,
        hasShownNoBranchDialog: this.hasShownNoBranchDialog,
        projectId,
        isMounted: this._isMounted,
        dataInitialized: this.dataInitialized
      })

      if (
        Array.isArray(branchOptions) &&
        branchOptions.length === 0 &&
        !this.branchLoading &&
        !this.hasShownNoBranchDialog &&
        projectId &&
        this._isMounted &&
        this.dataInitialized
      ) {
        // console.log('显示无分支提示弹窗')
        this.showNoBranchDialog = true
        this.hasShownNoBranchDialog = true
      }
    },

    // 取消弹窗
    handleCancelDialog() {
      this.showNoBranchDialog = false
    },

    // 前往分支管理页面
    handleGoToBranchManagement() {
      const projectId = this.$route.params.projectId || sessionStorage.getItem('currentProjectId')
      if (projectId) {
        this.$router.push(`/project/${projectId}/branch`)
        this.showNoBranchDialog = false
      } else {
        this.$message.error('未找到项目ID')
      }
    },

    // 更新项目信息
    async updateProjectInfo(projectId) {
      if (!projectId) {
        return
      }

      try {
        const response = await getProjectInfo({ projectId })
        if (response.code === 200 && response.data) {
          const { projectName } = response.data

          // 更新Vuex中的项目信息
          this.setProjectInfo({
            projectId,
            projectName: projectName || ''
          })

          // console.log('项目信息更新完成')
        } else {
          // console.warn('获取项目信息失败:', response)
        }
      } catch (error) {
        console.error('获取项目信息失败:', error)
        // 即使获取失败，也要设置projectId，避免影响其他功能
        this.setProjectInfo({
          projectId,
          projectName: ''
        })
      }
    }

  }
}
</script>

<style lang="scss" scoped>
.dropdown-selector-container {
  display: flex;
  align-items: center;
  gap: 20px;
  padding: 0 16px;
  height: 100%;

  .selector-item {
    display: flex;
    align-items: center;
    gap: 8px;

    .selector-label {
      font-size: 14px;
      color: #606266;
      white-space: nowrap;
      font-weight: 500;
    }

    .selector-dropdown {
      width: 160px;

      ::v-deep .el-input {
        height: 32px;

        .el-input__inner {
          height: 32px;
          line-height: 32px;
          font-size: 13px;
          border-radius: 4px;
          border: 1px solid #dcdfe6;
          transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);

          &:hover {
            border-color: #c0c4cc;
          }

          &:focus {
            border-color: #409eff;
            outline: none;
          }
        }

        .el-input__suffix {
          .el-input__suffix-inner {
            .el-select__caret {
              font-size: 12px;
              color: #c0c4cc;
              transition: transform 0.3s;
            }
          }
        }
      }

      &.is-focus {
        ::v-deep .el-input__inner {
          border-color: #409eff;
        }

        ::v-deep .el-select__caret {
          transform: rotateZ(180deg);
        }
      }
    }
  }
}

// 下拉面板样式
::v-deep .el-select-dropdown {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

  .el-select-dropdown__item {
    font-size: 13px;
    padding: 0 12px;
    height: 34px;
    line-height: 34px;
    color: #606266;

    &:hover {
      background-color: #f5f7fa;
    }

    &.selected {
      color: #409eff;
      font-weight: 500;
      background-color: #f0f9ff;
    }
  }
}

// 分支为空提示弹窗样式
.no-branch-content {
  text-align: center;
  padding: 20px 0;

  .warning-icon {
    font-size: 48px;
    color: #e6a23c;
    margin-bottom: 16px;
  }

  .warning-text {
    font-size: 16px;
    font-weight: 500;
    color: #303133;
    margin: 0 0 8px 0;
  }

  .suggestion-text {
    font-size: 14px;
    color: #606266;
    margin: 0;
  }
}

.dialog-footer {
  text-align: center;
  padding-top: 10px;

  .el-button {
    margin: 0 8px;
    min-width: 80px;
  }
}

// 修复弹窗层级问题
::v-deep .no-branch-dialog {
  z-index: 2001 !important;

  .el-dialog {
    z-index: 2001 !important;
  }
}

// 确保遮罩层在弹窗下方但覆盖页面内容
::v-deep .v-modal {
  z-index: 2000 !important;
}

// 响应式设计
@media (max-width: 768px) {
  .dropdown-selector-container {
    flex-direction: column;
    gap: 12px;
    padding: 8px 16px;

    .selector-item {
      width: 100%;
      justify-content: space-between;

      .selector-dropdown {
        width: 140px;
      }
    }
  }
}
</style>
